package com.xiaopeng.xpautotest.constant;

import android.content.Context;
import android.content.SharedPreferences;

import com.xiaopeng.xpautotest.BuildConfig;
import com.xiaopeng.xpautotest.community.utils.Constant;
import com.xiaopeng.xpautotest.community.utils.FileUtils;
import com.xiaopeng.xpautotest.community.utils.Log;

import static com.xiaopeng.xui.Xui.getContext;

public class EnvironmentConfig {
    private static EnvironmentConfig instance;
    private SharedPreferences prefs;
    private static final String KEY_ENV_TYPE = "env_type";
    public static final String KEY_FACTORY_MODE = "factory_mode";
    public static final String ENV_PRODUCTION = "prod";
    public static final String ENV_UAT = "uat";
    public static final String ENV_TEST = "test";

    // 工厂生产、预发、测试环境地址，生产和预发在工厂私有云，测试环境在logan阿里云上
    // 广州工厂南区生产环境
    private static final String FACTORY_PROD_URL = "https://vatc-gz02.x-peng.com";
    private static final String FACTORY_PROD_TOKEN = "b7376343b0fcd05fbe6605b4cc9eb100f8f6d542";
    private static final String FACTORY_PROD_OSS_ENDPOINT = "https://gzgc02-ali-prod-vctc-autotest.x-peng.com";
    private static final String FACTORY_PROD_OSS_BUCKET_NAME = "gzgc02-ali-prod-vctc-autotest";
    private static final String FACTORY_PROD_OSS_ACCESS_KEY = "zbJTs7MAG7ps7xkH";
    private static final String FACTORY_PROD_OSS_SECURITY_KEY = "89Pe53q5EUsl9uSGnfIU1Nq44H3QUQ";
    // 广州工厂预发环境
    private static final String FACTORY_UAT_URL = "https://vatc-gz02-uat.x-peng.com";
    private static final String FACTORY_UAT_TOKEN = "ef6e60a384bdae5acefc95824c30e2c9d4798601";
    private static final String FACTORY_UAT_OSS_ENDPOINT = "https://gzgc-ali-uat-vctc-autotest.x-peng.com";
    private static final String FACTORY_UAT_OSS_BUCKET_NAME = "gzgc-ali-uat-vctc-autotest";
    private static final String FACTORY_UAT_OSS_ACCESS_KEY = "mjhNUO1MowPzXUDP";
    private static final String FACTORY_UAT_OSS_SECURITY_KEY = "ALGSk12vXCPjyWbula1zdJoHqRz0oH";
    // 阿里云测试环境
    private static final String FACTORY_TEST_URL = "https://xp-autotest-test.x-peng.com";
    private static final String FACTORY_TEST_TOKEN = "77348a32275ff6cd7ff61cb4d865e3a461932cb6";

    // 办公网正式、测试环境地址
    private static final String INTER_RELEASE_URL = "http://10.192.39.84:8899";
    private static final String INTER_RELEASE_TOKEN = "05b085c5a831a3223f3f7e1e1e3fae7c5f22967d";
    private static final String INTER_DEBUG_URL = "http://10.193.22.215:8081";
    private static final String INTER_DEBUG_TOKEN = "8c6609e401b54e8629b45a74d53156a818bccef8";
    private static final String INTER_OSS_ENDPOINT = "http://10.192.39.84:9000";
    private static final String INTER_OSS_BUCKET_NAME = "xpautotest";
    private static final String INTER_OSS_ACCESS_KEY = "Sgf48ZEYZvqsNxC2ETZb";
    private static final String INTER_OSS_SECURITY_KEY = "UdGzyzX4XwhDK8adEAuXcwKkG6M9VAV2GQuQUik1";

    private OSSConfig ossConfig;

    // 私有构造方法（单例）
    private EnvironmentConfig(Context context) {
        prefs = context.getSharedPreferences("EnvConfig", Context.MODE_PRIVATE);
    }

    public static synchronized EnvironmentConfig getInstance() {
        if (instance == null) {
            instance = new EnvironmentConfig(getContext().getApplicationContext());
        }
        return instance;
    }

    /**
     * 设置服务端环境
     * @param factoryMode 是否为工厂模式，true 为工厂模式，false 为办公网模式
     * @param envType 环境类型，"test" 为测试环境，"uat" 为预发环境，其他为正式环境
     * **/
    public void setEnvironment(boolean factoryMode, String envType) {
        String customerEnv = FileUtils.readFileContent(Constant.ENV_CONFIG_FILE);
        customerEnv = customerEnv == null ? "" : customerEnv.trim();
        if (!customerEnv.isEmpty()) {
            if (customerEnv.startsWith("factory_")) {
                factoryMode = true;
                envType = customerEnv.substring(7).trim();
            } else {
                factoryMode = false;
                envType = customerEnv.substring(6).trim();
            }
            Log.i("EnvironmentConfig", "Custom environment: " + envType);
        }
        String baseUrl;
        String apiKey;
        switch (envType) {
            case ENV_TEST:
                baseUrl = factoryMode ? FACTORY_TEST_URL : INTER_DEBUG_URL;
                apiKey = factoryMode ? FACTORY_TEST_TOKEN : INTER_DEBUG_TOKEN;
                break;
            case ENV_UAT:
                baseUrl = factoryMode ? FACTORY_UAT_URL : INTER_RELEASE_URL;
                apiKey = factoryMode ? FACTORY_UAT_TOKEN : INTER_RELEASE_TOKEN;
                break;
            default:
                baseUrl = factoryMode ? FACTORY_PROD_URL : INTER_RELEASE_URL;
                apiKey = factoryMode ? FACTORY_PROD_TOKEN : INTER_RELEASE_TOKEN;
                break;
        }

        Log.i("EnvironmentConfig", "mode" + factoryMode + ", Base URL: " + baseUrl);
        prefs.edit().putBoolean(KEY_FACTORY_MODE, factoryMode).apply();
        prefs.edit().putString(KEY_ENV_TYPE, envType).apply();
        prefs.edit().putString("CUSTOM_BASE_URL", baseUrl).apply();
        prefs.edit().putString("CUSTOM_API_KEY", apiKey).apply();
    }

    // 获取当前环境 BaseURL
    public String getBaseUrl() {
        return prefs.getString("CUSTOM_BASE_URL", INTER_RELEASE_URL) + "/autotest/test/api/v1.0/";
    }

    // 动态切换环境
    public void switchEnvironment(String baseUrl) {
        prefs.edit().putString("CUSTOM_BASE_URL", baseUrl).apply();
    }

    // 获取 API Key（根据环境自动选择）
    public String getApiKey() {
        return prefs.getString("CUSTOM_API_KEY", INTER_RELEASE_TOKEN);
    }

    public OSSConfig getOssConfig() {
        boolean isFactoryMode = prefs.getBoolean(KEY_FACTORY_MODE, false);
        String envType = prefs.getString(KEY_ENV_TYPE, ENV_PRODUCTION);
        if (isFactoryMode) {
            switch (envType) {
                case ENV_PRODUCTION:
                    return new OSSConfig(FACTORY_PROD_OSS_ENDPOINT, FACTORY_PROD_OSS_BUCKET_NAME, FACTORY_PROD_OSS_ACCESS_KEY, FACTORY_PROD_OSS_SECURITY_KEY);
                default:
                    return new OSSConfig(FACTORY_UAT_OSS_ENDPOINT, FACTORY_UAT_OSS_BUCKET_NAME, FACTORY_UAT_OSS_ACCESS_KEY, FACTORY_UAT_OSS_SECURITY_KEY);
            }
        } else {
            return new OSSConfig(INTER_OSS_ENDPOINT, INTER_OSS_BUCKET_NAME, INTER_OSS_ACCESS_KEY, INTER_OSS_SECURITY_KEY);
        }
    }

    public class OSSConfig {
        private String endpoint;
        private String bucketName;
        private String accessKey;
        private String securityKey;

        public OSSConfig(String endpoint, String bucketName, String accessKey, String securityKey) {
            this.endpoint = endpoint;
            this.bucketName = bucketName;
            this.accessKey = accessKey;
            this.securityKey = securityKey;
        }

        public String getEndpoint() {
            return endpoint;
        }

        public String getBucketName() {
            return bucketName;
        }

        public String getAccessKey() {
            return accessKey;
        }

        public String getSecurityKey() {
            return securityKey;
        }
    }

    public String getWSUrl(String vin, String model, String version) {
        String baseUrl = prefs.getString("CUSTOM_BASE_URL", INTER_RELEASE_URL);
//        String baseUrl = "http://10.194.59.109:8000";
        return String.format("%s/ws/realtime/vehicle/%s?model=%s&version=%s", baseUrl.replace("http", "ws"), vin, model, version);
    }
}
