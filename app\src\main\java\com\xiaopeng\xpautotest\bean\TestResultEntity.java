package com.xiaopeng.xpautotest.bean;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class TestResultEntity implements Parcelable, ReportableEntity {
    private long taskExecutionId;
    private long id;
    private int status = -1;
    private long startTime;
    private long endTime;
    private long duration;
    private String description;
    private boolean uploaded = false;
    private long timestamp; // 时间戳
    private long triggerTimestamp;  // 触发时间戳
    private long suiteId;

    // 批量上报优化：步骤执行结果列表
    private List<StepResultEntity> stepResults;
    // 批量上报优化：CAN数据追踪结果列表
    private List<TraceResultEntity> traceResults;

    public TestResultEntity(long taskExecutionId, long id, int status) {
        this.taskExecutionId = taskExecutionId;
        this.id = id;
        this.status = status;
        this.timestamp = System.currentTimeMillis();
        this.stepResults = new ArrayList<>();
        this.traceResults = new ArrayList<>();
    }

    public TestResultEntity(long taskExecutionId, long id, int status, long startTime, long endTime, String description) {
        this.taskExecutionId = taskExecutionId;
        this.id = id;
        this.status = status;
        this.startTime = startTime;
        this.endTime = endTime;
        this.description = description;
        this.timestamp = System.currentTimeMillis();
        this.stepResults = new ArrayList<>();
        this.traceResults = new ArrayList<>();
    }

    protected TestResultEntity(Parcel in) {
        taskExecutionId = in.readLong();
        id = in.readLong();
        status = in.readInt();
        startTime = in.readLong();
        endTime = in.readLong();
        description = in.readString();
        uploaded = in.readByte() != 0;
        timestamp = in.readLong();
        triggerTimestamp = in.readLong();
        suiteId = in.readLong();
        stepResults = in.createTypedArrayList(StepResultEntity.CREATOR);
        traceResults = in.createTypedArrayList(TraceResultEntity.CREATOR);
    }

    public static final Creator<TestResultEntity> CREATOR = new Creator<TestResultEntity>() {
        @Override
        public TestResultEntity createFromParcel(Parcel in) {
            return new TestResultEntity(in);
        }

        @Override
        public TestResultEntity[] newArray(int size) {
            return new TestResultEntity[size];
        }
    };

    public long getScriptId() {
        return id;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public boolean getUploaded() {
        return uploaded;
    }

    public void setUploaded(boolean uploaded) {
        this.uploaded = uploaded;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(taskExecutionId);
        dest.writeLong(id);
        dest.writeInt(status);
        dest.writeLong(startTime);
        dest.writeLong(endTime);
        dest.writeString(description);
        dest.writeByte((byte) (uploaded ? 1 : 0));
        dest.writeLong(timestamp);
        dest.writeLong(triggerTimestamp);
        dest.writeLong(suiteId);
        dest.writeTypedList(stepResults);
        dest.writeTypedList(traceResults);
    }

    @Override
    public long getTaskExecutionId() {
        return taskExecutionId;
    }

    public void setTriggerTimestamp(long triggerTimestamp) {
        this.triggerTimestamp = triggerTimestamp;
    }

    public void setSuiteId(long suiteId) {
        this.suiteId = suiteId;
    }
    /**
     * 设置步骤执行结果列表
     */
    public void setStepResults(List<StepResultEntity> stepResults) {
        this.stepResults = stepResults;
    }

    /**
     * 设置CAN数据追踪结果列表
     */
    public void setTraceResults(List<TraceResultEntity> traceResults) {
        this.traceResults = traceResults;
    }
}
