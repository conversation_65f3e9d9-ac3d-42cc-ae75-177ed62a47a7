# 配置化聚合上传实现方案

## 概述

为了提供更灵活的数据上报方式，我们实现了基于配置项 `isCombineUpload` 的聚合上传功能。用户可以通过配置文件控制是否启用聚合上传，在性能优化和实时性之间进行选择。

## 配置项设计

### 1. TestTaskBean 配置字段

在 `TestTaskBean` 中添加了 `isCombineUpload` 配置字段：

```java
public class TestTaskBean {
    @SerializedName("isCombineUpload")
    private boolean isCombineUpload = true; // 默认启用聚合上传
    
    public boolean isCombineUpload() {
        return isCombineUpload;
    }
    
    public void setCombineUpload(boolean combineUpload) {
        isCombineUpload = combineUpload;
    }
}
```

### 2. 配置文件格式

配置文件 `taskInfo.json` 示例：

```json
{
    "id": 12345,
    "name": "测试任务",
    "executionId": 67890,
    "suiteId": 111,
    "isCombineUpload": true,
    "autoStart": false,
    "blackActivityNames": []
}
```

### 3. 配置项说明

| 配置值 | 行为 | 适用场景 |
|--------|------|----------|
| `true` | 聚合上传模式 | 性能优先，减少网络请求 |
| `false` | 分别上传模式 | 实时性优先，立即上报数据 |

## 实现逻辑

### 1. 脚本执行完成时的处理

```java
// 聚合上传模式：将收集的步骤和CAN数据添加到TestResultEntity中一起上报
if (mTaskInfo.isCombineUpload()) {
    result.setStepResults(new ArrayList<>(mCurrentScriptStepResults));
    result.setTraceResults(new ArrayList<>(mCurrentScriptTraceResults));
    FileLogger.i(TAG, "Combine upload: added " +
        mCurrentScriptStepResults.size() + " steps and " +
        mCurrentScriptTraceResults.size() + " traces to TestResultEntity");
} else {
    // 分别上传模式：步骤和CAN数据已在回调中立即上报，这里不需要处理
    FileLogger.i(TAG, "Separate upload: steps and traces already reported in callbacks");
}
```

**关键说明**：
- 聚合模式：在脚本完成时将收集的数据添加到 `TestResultEntity` 中
- 分别模式：数据已在步骤执行回调中立即上报，脚本完成时无需重复处理

### 2. 步骤执行时的处理

```java
@Override
public void onStepSuccess(TestStep testStep, TestResult testResult) {
    StepResultEntity stepResult = new StepResultEntity(...);
    
    if (mTaskInfo.isCombineUpload()) {
        // 聚合上传：收集到列表中，不立即上报
        mCurrentScriptStepResults.add(stepResult);
        FileLogger.i(TAG, "Collected step result for combine upload");
    } else {
        // 分别上传：立即上报
        TestReporterSDK.reportStep(stepResult);
        FileLogger.i(TAG, "Immediately reported step result");
    }
}
```

### 3. CAN数据处理

```java
@Subscribe(threadMode = ThreadMode.BACKGROUND)
public void onTraceResultEvent(TraceEvent.ResultEvent event) {
    TraceResultEntity traceResult = new TraceResultEntity(...);
    
    if (mTaskInfo.isCombineUpload()) {
        // 聚合上传：收集到列表中，不立即上报
        mCurrentScriptTraceResults.add(traceResult);
        FileLogger.i(TAG, "Collected trace result for combine upload");
    } else {
        // 分别上传：立即上报
        TestReporterSDK.reportStep(traceResult);
        FileLogger.i(TAG, "Immediately reported trace result");
    }
}
```

## 两种模式对比

### 聚合上传模式 (isCombineUpload = true)

**优势**：
- 减少网络请求次数，提升性能
- 降低服务端处理压力
- 数据完整性更好，事务性更强

**劣势**：
- 实时性较差，需要等到脚本完成才能看到步骤结果
- 内存占用稍高（需要缓存数据）

**执行流程**：
```
步骤1执行 → 收集到列表（不上报）
步骤2执行 → 收集到列表（不上报）
...
CAN数据1 → 收集到列表（不上报）
CAN数据2 → 收集到列表（不上报）
...
脚本完成 → TestResultEntity(包含所有数据) → 一次性上报
```

### 分别上传模式 (isCombineUpload = false)

**优势**：
- 实时性好，步骤执行完立即可见结果
- 内存占用低，不需要缓存数据
- 更接近原有的实时上报行为

**劣势**：
- 网络请求频繁，可能影响性能
- 服务端处理压力较大

**执行流程**：
```
步骤1执行 → 立即上报
步骤2执行 → 立即上报
...
CAN数据1 → 立即上报
CAN数据2 → 立即上报
...
脚本完成 → TestResultEntity(不含步骤和CAN数据) → 上报
```

## 配置建议

### 1. 性能优先场景
- 大量步骤的测试脚本
- 网络环境较差的情况
- 服务端处理能力有限
- **建议设置**：`"isCombineUpload": true`

### 2. 实时性优先场景
- 调试模式下的测试
- 需要实时监控步骤执行状态
- 测试脚本步骤较少
- **建议设置**：`"isCombineUpload": false`

### 3. 默认配置
- 系统默认启用聚合上传 (`true`)
- 在大多数生产环境下推荐使用聚合模式
- 可根据具体需求动态调整

## 兼容性保证

### 1. 向后兼容
- 如果配置文件中没有 `isCombineUpload` 字段，默认为 `true`
- 保持原有API接口不变
- 服务端无需修改，能够处理两种模式的数据

### 2. 配置热更新
- 配置项在任务启动时读取
- 同一任务执行期间配置保持不变
- 新任务会读取最新的配置

## 监控和日志

### 1. 日志记录
- 记录当前使用的上传模式
- 记录聚合上传时收集的数据量
- 记录分别上传时的实时上报情况

### 2. 性能监控
- 聚合模式：记录批量上报的数据量和耗时
- 分别模式：记录单次上报的频率和响应时间

## 总结

通过 `isCombineUpload` 配置项，我们实现了灵活的数据上报策略：

1. **配置驱动**：通过简单的布尔值控制上报行为
2. **性能可调**：在性能和实时性之间灵活选择
3. **向后兼容**：保持原有功能不受影响
4. **易于维护**：统一的配置管理和日志记录

这种设计让用户能够根据具体的测试场景和需求，选择最适合的数据上报方式。
