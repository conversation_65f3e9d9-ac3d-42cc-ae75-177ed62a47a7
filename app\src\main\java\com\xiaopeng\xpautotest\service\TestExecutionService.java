package com.xiaopeng.xpautotest.service;

import android.app.Service;
import android.content.Intent;
import android.os.Binder;
import android.os.IBinder;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;

import com.xiaopeng.executor.TestExecutor;
import com.xiaopeng.executor.BaseTestExecutor;
import com.xiaopeng.xpautotest.R;
import com.xiaopeng.xpautotest.bean.TestStartedEntity;
import com.xiaopeng.xpautotest.bean.TestStopEntity;
import com.xiaopeng.xpautotest.community.event.StopTestEvent;
import com.xiaopeng.xpautotest.community.event.TraceEvent;
import com.xiaopeng.xpautotest.community.event.TraceResultState;
import com.xiaopeng.xpautotest.community.test.FailureReasonDetail;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.test.TestScript;
import com.xiaopeng.xpautotest.community.test.TestStep;
import com.xiaopeng.xpautotest.community.utils.Constant;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.community.bean.CaseExecuteState;
import com.xiaopeng.xpautotest.bean.StepResultEntity;
import com.xiaopeng.xpautotest.bean.TestFinishEntity;
import com.xiaopeng.xpautotest.bean.TestResultEntity;
import com.xiaopeng.xpautotest.bean.TestScriptEntity;
import com.xiaopeng.xpautotest.bean.TestTaskBean;
import com.xiaopeng.xpautotest.bean.TraceResultEntity;
import com.xiaopeng.xpautotest.client.TestReporterSDK;
import com.xiaopeng.xpautotest.client.api.ApiResponse;
import com.xiaopeng.xpautotest.client.api.TaskApiImpl;
import com.xiaopeng.xpautotest.community.utils.LogTagHelper;
import com.xiaopeng.xpautotest.community.config.GlobalConfig;
import com.xiaopeng.xpautotest.helper.DialogHelper;
import com.xiaopeng.xpautotest.helper.TestDataStorageHelper;
import com.xiaopeng.xpautotest.ui.MainActivity;
import com.xiaopeng.xpautotest.ui.TestingFloatingView;
import com.xiaopeng.xpautotest.trace.service.CanDataCollectService;
import com.xiaopeng.xui.app.XDialogInterface;
import com.xiaopeng.xpautotest.bean.UploadFileTask;
import com.xiaopeng.xpautotest.utils.OSSPathUtils;
import com.xiaopeng.xpautotest.helper.LogProcessHelper;
import com.xiaopeng.xpautotest.helper.log.model.LogProcessResult;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import androidx.core.app.NotificationCompat;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import static com.xiaopeng.xui.Xui.getContext;

public class TestExecutionService extends Service {
    private static final String TAG = "TestExecutionService";
    public static final String MODE_ALL = "all";    // 全量执行所有测试用例
    public static final String MODE_SUITE = "suite";    // 全量执行该场景测试用例
    public static final String MODE_RETRY = "retry";    // 重试失败的测试用例
    public static final int NOTIFICATION_ID = 1001;
    private TestExecutor testExecutor;

    private ExecutorService executor;
    private TestTaskBean mTaskInfo;
    private TestStartedEntity mTaskExecutionInfo;
    private TestingFloatingView mFloatingView;
    private volatile boolean mIsCanParseSuccess; // Can信号解析结果
    private boolean mWasManuallyStopped = false; // 是否手动停止了测试执行
    private long mTestStartTimestamp; // 统一的测试开始时间
    private long mTestEndTimestamp; // 统一的测试结束时间
    private final LogProcessHelper mLogProcessHelper = new LogProcessHelper(); // 日志处理器

    // 批量上报优化：当前脚本的步骤和CAN数据收集
    private List<StepResultEntity> mCurrentScriptStepResults = new ArrayList<>();
    private List<TraceResultEntity> mCurrentScriptTraceResults = new ArrayList<>();

    // 添加实例标志位，用于跟踪服务是否已经在执行测试任务，onDestroy()后自动销毁
    private volatile boolean isExecutingTest = false;

    @Override
    public void onCreate() {
        super.onCreate();
        FileLogger.i(TAG, "onCreate()");
        executor = Executors.newSingleThreadExecutor();
//        resultLiveData = new MutableLiveData<>();
//        startForeground(NOTIFICATION_ID, buildNotification("测试准备中..."));

        // Register EventBus
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        // 启动Can数据采集服务
        Intent intent = new Intent(this, CanDataCollectService.class);
//        intent.putExtra(com.xiaopeng.xpautotest.trace.constant.Constant.Service.MOCK_MODE, true);
        startService(intent);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        FileLogger.i(TAG, "onStartCommand()");
        if (intent == null) {
            FileLogger.e(TAG, "intent is null");
            return START_NOT_STICKY;
        }

        // 检查是否已经在执行测试任务
        if (isExecutingTest) {
            FileLogger.w(TAG, "Already executing test, ignoring this start request");
            return START_NOT_STICKY;
        }
        
        // 设置标志位，表示开始执行测试任务
        isExecutingTest = true;

        mFloatingView = new TestingFloatingView(this, () -> {
            FileLogger.w(TAG, "onClicked testing floating view");
            this.testExecutor.pauseExecution();
            showFinishConfirmationDialog();
        });
        createNotificationChannel();
        startForeground(NOTIFICATION_ID, buildRunningNotification());

        String mode = intent.getStringExtra(Constant.EXTRA_TEST_MODE);
        if (Objects.equals(mode, MODE_ALL)) {
            TestDataStorageHelper.getInstance().clearResultSync();
        }

        long executionId = intent.getLongExtra(Constant.EXTRA_EXECUTION_ID, 0);
        // 统一的测试开始时间
        mTestStartTimestamp = System.currentTimeMillis();
        FileLogger.i(TAG, "Test started for executionId: " + executionId);

        if (mTaskInfo == null) {
            mTaskInfo = TestDataStorageHelper.getInstance().loadTaskSync();
            if (mTaskInfo != null) {
                mTaskInfo.setExecutionId(executionId);
                mTaskInfo.setSuiteId(intent.getLongExtra(Constant.EXTRA_TEST_SUITE_ID, 0));
                // 初始化全局配置
                GlobalConfig.getInstance().setBlackActivityNames(mTaskInfo.getBlackActivityNames());
            }
        }
        FileLogger.getInstance().startExecutionLog(String.valueOf(executionId), mTestStartTimestamp);
        FileLogger.i(TAG, "TestTaskInfo: " + mTaskInfo);

        int taskType = Objects.equals(mode, MODE_ALL) || Objects.equals(mode, MODE_SUITE) ? 1 : 2;
        List<TestScriptEntity> testScripts = intent.getParcelableArrayListExtra(Constant.EXTRA_TEST_SCRIPTS);
        if (testScripts != null && !testScripts.isEmpty()) {
            ArrayList<Long> pendingScriptIdList = new ArrayList<>();
            for (TestScriptEntity script : testScripts) {
                pendingScriptIdList.add(script.getScriptId());
            }
            mTaskExecutionInfo = new TestStartedEntity(mTaskInfo.getExecutionId(), taskType, mTestStartTimestamp, mTaskInfo.getSuiteId(), pendingScriptIdList);
            TestReporterSDK.reportStep(mTaskExecutionInfo);

            executeTestScripts(testScripts);
        } else {
            FileLogger.w(TAG, "No test cases to execute");
        }
        return super.onStartCommand(intent, flags, startId);
    }

    public void finishTest() {
        FileLogger.i(TAG," Test finished for executionId: " + mTaskInfo.getExecutionId());
        // 统一的测试结束时间
        mTestEndTimestamp = System.currentTimeMillis();
        TestFinishEntity testFinishEntity = new TestFinishEntity(mTaskInfo.getExecutionId(), mTestStartTimestamp, mTaskExecutionInfo.getSuiteId(), mTaskExecutionInfo.getTestCount(), mTaskExecutionInfo.getOkCount(), mTaskExecutionInfo.getNgCount(),mTaskExecutionInfo.getNaCount(), mTaskExecutionInfo.getPendingScriptIdList());

        // 日志处理
        long logProcessStartTime = System.currentTimeMillis();

        LogProcessResult logResult = mLogProcessHelper
                .processAllLogs(mTestStartTimestamp, mTestEndTimestamp, mTaskExecutionInfo);

        long logProcessDuration = System.currentTimeMillis() - logProcessStartTime;
        FileLogger.i(TAG, "Log processing completed in " + logProcessDuration + "ms, hasErrors: " + logResult.hasErrors());

        applyLogResultsToEntity(testFinishEntity, logResult);

        // 上报测试完成实体（包含CDU日志路径，如果有的话）
        TestReporterSDK.reportStep(testFinishEntity);

//        TaskApiImpl taskApi = new TaskApiImpl();
//        taskApi.finishTest(mTaskInfo.getExecutionId(), new Callback<ApiResponse>() {
//            @Override
//            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
//                if (response.isSuccessful()) {
//                    Log.i(TAG, "finishTest onResponse: " + response.body());
//                } else {
//                    Log.e(TAG, "finishTest onResponse Server error: " + response.code());
//                }
//            }
//
//            @Override
//            public void onFailure(Call<ApiResponse> call, Throwable t) {
//                Log.e(TAG, "finishTest onFailure: " + t.getMessage());
//            }
//        });
        EventBus.getDefault().post(new TraceEvent.EndEvent());

        // 1. 先关闭悬浮窗，清理当前UI
        if (mFloatingView != null) {
            mFloatingView.close();
            mFloatingView = null;
        }

        // 2. 再启动新的Activity
        Intent intent = new Intent(TestExecutionService.this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        intent.putExtra(Constant.EXTRA_TEST_DATA_LOAD_TAG, "local");
        intent.putExtra(Constant.EXTRA_TEST_SUITE_ID, mTaskInfo.getSuiteId());
        if (mWasManuallyStopped) {
            intent.putExtra(Constant.EXTRA_MANUAL_STOP_TASK_ID, mTaskInfo.getId());
            TestReporterSDK.reportStep(new TestStopEntity(mTaskInfo.getExecutionId()));
        }
        startActivity(intent);

        // 3. 最后清理Service自身
        if (testExecutor != null) {
            testExecutor.removeExecuteHandler();
        }
        if (executor != null) {
            executor.shutdownNow();
        }
        stopService(new Intent(this, CanDataCollectService.class));
        stopSelf();
    }

    private void executeTestScripts(List<TestScriptEntity> testScripts) {
        FileLogger.i(TAG, "executeTestScripts: script count=" + testScripts.size());

        executor.execute(() -> {
            testExecutor = new TestExecutor(getApplicationContext(), getPackageName());
            testExecutor.addExecuteHandler(mExecuteHandler);
            int total = testScripts.size();
            mFloatingView.updateProgress("0/" + total);

            for (int i=0; i < total; i++) {
                TestScriptEntity testScript = testScripts.get(i);
                mIsCanParseSuccess = true; // 清空上次执行的Can结果

                // 清空当前脚本的收集列表
                mCurrentScriptStepResults.clear();
                mCurrentScriptTraceResults.clear();

                TestResultEntity result = executeSingleTestScript(testScript);

                // 创建副本避免异步上报时数据被清空
                result.setStepResults(new ArrayList<>(mCurrentScriptStepResults));
                result.setTraceResults(new ArrayList<>(mCurrentScriptTraceResults));

                reportTestResult(result);
//                resultLiveData.postValue(result);
                if (mFloatingView != null) {
                    mFloatingView.updateProgress((i + 1) + "/" + total);
                }
//                if (i + 1 == total) {
//                    saveTestResult(result, false);
//                }
            }
            finishTest();
        });
    }

    /**
     * 执行单个测试脚本
     * */
    private TestResultEntity executeSingleTestScript(TestScriptEntity testScript) {
        // 实际执行逻辑
        TestReporterSDK.reportStep(new TestResultEntity(mTaskInfo.getExecutionId(), testScript.getScriptId(), CaseExecuteState.EXECUTING));
        boolean isNeedTrace = testScript.needTrace();
        if (isNeedTrace) {
            EventBus.getDefault().post(new TraceEvent.StartEvent());
            FileLogger.i(TAG, "Send StartEvent: " + testScript.getScriptId());
        }
        long startTime = System.currentTimeMillis();
        LogTagHelper.logScriptStart(testScript.getScriptId(),startTime);
        int result = testExecutor.runTestScript(testScript.getScriptId(), testScript.getSteps());
        long endTime = System.currentTimeMillis();
        if (isNeedTrace) {
            // 发送测试结束事件，同步执行
            EventBus.getDefault().post(new TraceEvent.EndEvent()); // 发送测试结束事件
            FileLogger.i(TAG, "Send EndEvent: " + testScript.getScriptId());
        }
        if (!mIsCanParseSuccess && result != CaseExecuteState.SKIPPED) {
            result = CaseExecuteState.FAILURE; // 如果Can数据解析失败，将结果设置为失败
        }
        // int status = ((result == CaseExecuteState.SUCCESS) && mIsCanParseSuccess) ? CaseExecuteState.SUCCESS : CaseExecuteState.FAILURE;
        FileLogger.i(TAG, "finish testcase: " + testScript.getScriptId() + ", result: " + result + ", canParseResult: " + mIsCanParseSuccess);
        LogTagHelper.logScriptEnd(testScript.getScriptId(), CaseExecuteState.getResultString(result), endTime-startTime);
        mTaskInfo.addExecuteCount(result);
        mTaskExecutionInfo.addExecuteCount(result);
        return new TestResultEntity(mTaskInfo.getExecutionId(), testScript.getScriptId(), result, startTime, endTime, "");
    }

    /**
     * 上报测试脚本执行结果
     * */
    private void reportTestResult(TestResultEntity result) {
        result.setTriggerTimestamp(mTaskExecutionInfo.getTriggerTimestamp());
        result.setSuiteId(mTaskExecutionInfo.getSuiteId());
        saveTestResult(result, false);
        TestReporterSDK.reportStep(result);

//        TaskApiImpl taskApi = new TaskApiImpl();
//        if (mTaskInfo.getExecutionId() == 0) {
//            Log.e(TAG, "reportResult: executionId is 0");
//            saveTestResult(result, false);
//            return;
//        }
//        taskApi.reportResult(mTaskInfo.getExecutionId(), result, new Callback<ApiResponse>() {
//            @Override
//            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
//                if (response.isSuccessful()) {
//                    Log.i(TAG, "reportResult onResponse: " + response.body());
//                    saveTestResult(result, true);
//                } else {
//                    Log.e(TAG, "reportResult onResponse Server error: " + response.code());
//                    saveTestResult(result, false);
//                }
//            }
//
//            @Override
//            public void onFailure(Call<ApiResponse> call, Throwable t) {
//                Log.e(TAG, "reportResult onFailure: " + t.getMessage());
//                saveTestResult(result, false);
//            }
//        });
    }

    /**
     * 上报脚本中的测试步骤执行结果
     * */
    private void reportStepResult(StepResultEntity stepResult) {
//        TaskApiImpl taskApi = new TaskApiImpl();
//        if (mTaskInfo.getExecutionId() == 0) {
//            Log.e(TAG, "reportStepResult: executionId is 0");
//            return;
//        }
//        taskApi.reportStepResult(mTaskInfo.getExecutionId(), stepResult, new Callback<ApiResponse>() {
//            @Override
//            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
//                if (response.isSuccessful()) {
//                    Log.i(TAG, "reportStepResult onResponse: " + response.body());
//                } else {
//                    Log.e(TAG, "reportStepResult onResponse Server error: " + response.code());
//                }
//            }
//
//            @Override
//            public void onFailure(Call<ApiResponse> call, Throwable t) {
//                Log.e(TAG, "reportTraceResult onFailure: " + t.getMessage());
//            }
//        });
    }

    /**
     * 上报脚本中的测试步骤执行结果
     * */
    private void reportTraceResult(TraceResultEntity traceResult) {
        TaskApiImpl taskApi = new TaskApiImpl();
        if (mTaskInfo.getExecutionId() == 0) {
            FileLogger.e(TAG, "reportTraceResult: executionId is 0");
            return;
        }
        taskApi.reportTraceResult(mTaskInfo.getExecutionId(), traceResult, new Callback<ApiResponse>() {
            @Override
            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                if (response.isSuccessful()) {
                    FileLogger.i(TAG, "reportTraceResult onResponse: " + response.body());
                } else {
                    FileLogger.e(TAG, "reportTraceResult onResponse Server error: " + response.code());
                }
            }

            @Override
            public void onFailure(Call<ApiResponse> call, Throwable t) {
                FileLogger.e(TAG, "reportTraceResult onFailure: " + t.getMessage());
            }
        });
    }

    private void saveTestResult(TestResultEntity result, boolean uploaded) {
        result.setUploaded(uploaded);
        // 保存测试结果
        TestDataStorageHelper.getInstance().saveMapResult(result, success -> {
//            if (success) loadHistory();
        });
    }

    private final BaseTestExecutor.ExecutionHandler mExecuteHandler = new BaseTestExecutor.ExecutionHandler() {
        @Override
        public void onStepSuccess(TestStep testStep, TestResult testResult) {
            StepResultEntity stepResult = new StepResultEntity(mTaskInfo.getExecutionId(), testStep.getScriptId(), testStep.getStepId(), testStep.getStep(),testStep.getComments(),testStep.getKeywords(),testStep.getStepType(),"PASS", testResult.getStartTime(), testResult.getEndTime());
            // 如果有截图UIDump就要对传oss路径
//            if (testStep.getAction().contains("ScreenCap") || testStep.getAction().contains("UIDump") ) {
            if (testResult.getData() instanceof TestResult.ActionArtifacts) {
                setStepArtifacts(stepResult, testResult);
            }

            // 批量上报优化：收集到列表中，不立即上报
            mCurrentScriptStepResults.add(stepResult);
            FileLogger.i(TAG, "Collected step result for batch reporting: scriptId=" + testStep.getScriptId() + ", stepId=" + testStep.getStepId());
        }

        @Override
        public void onStepFailure(TestStep testStep, TestResult testResult) {
            StepResultEntity stepResult = new StepResultEntity(mTaskInfo.getExecutionId(), testStep.getScriptId(), testStep.getStepId(),testStep.getStep(),testStep.getComments(),testStep.getKeywords(),testStep.getStepType(), "FAIL", testResult.getStartTime(), testResult.getEndTime());
            FailureReasonDetail failureReason = testResult.getFailureReason();
            if (failureReason != null) {
                stepResult.setFailureReason(failureReason);
            }
            setStepArtifacts(stepResult, testResult);

            // 批量上报优化：收集到列表中，不立即上报
            mCurrentScriptStepResults.add(stepResult);
            FileLogger.i(TAG, "Collected step failure result for batch reporting: scriptId=" + testStep.getScriptId() + ", stepId=" + testStep.getStepId());
        }

        private void setStepArtifacts(StepResultEntity stepResult, TestResult testResult) {
            if (testResult.getData()!=null && testResult.getData() instanceof TestResult.ActionArtifacts) {
                TestResult.ActionArtifacts artifactsData = (TestResult.ActionArtifacts) testResult.getData();
                String originalScreenshotFileName = artifactsData.imgFileName;
                String originalUiDumpFileName = artifactsData.uiDumpFileName;
                if (originalScreenshotFileName != null && !originalScreenshotFileName.isEmpty()) {
                    String imgPath = OSSPathUtils.buildObjectKey(UploadFileTask.FileType.FILE_IMAGE, originalScreenshotFileName, null);
                    stepResult.setImgPath(imgPath);
                }
                if (originalUiDumpFileName != null && !originalUiDumpFileName.isEmpty()) {
                    String uiDumpPath = OSSPathUtils.buildObjectKey(UploadFileTask.FileType.FILE_XML, originalUiDumpFileName, null);
                    stepResult.setDumpPath(uiDumpPath);
                }
            }
        }

        @Override
        public void onScriptSuccess(TestScript testScript) {

        }
    };

    /**
     * 接收trace解析结果
     * */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onTraceResultEvent(TraceEvent.ResultEvent event) {
        FileLogger.i(TAG, "onTraceResultEvent: " + event.toString());
        TraceResultEntity traceResult = new TraceResultEntity(mTaskInfo.getExecutionId(), event.getScriptId(), event.getStepId(), event.getTraceId(), event.getType(), event.getSrc(), event.getValue(), event.getResult(), event.getMessage());
        if (!event.getResult().equals(TraceResultState.OK)) {
            // 如果结果不是OK，记录Can数据采集结果
            mIsCanParseSuccess = false;
        }

        // 批量上报优化：收集到列表中，不立即上报
        mCurrentScriptTraceResults.add(traceResult);
        FileLogger.i(TAG, "Collected trace result for batch reporting: scriptId=" + event.getScriptId() + ", stepId=" + event.getStepId() + ", traceId=" + event.getTraceId());
    }

    /**
     * 停止执行测试任务事件
     * */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onStopTestEvent(StopTestEvent event) {
        FileLogger.i(TAG, "onStopTestEvent: " + event.toString());
        if (testExecutor != null) {
            testExecutor.stopExecution();
        }
        mWasManuallyStopped = true;
        finishTest();
    }

    private void createNotificationChannel() {
        NotificationChannel channel = new NotificationChannel(
                "test_service_channel",
                "测试服务",
                NotificationManager.IMPORTANCE_LOW
        );
        channel.setDescription("显示测试执行状态");
        NotificationManager manager = getSystemService(NotificationManager.class);
        manager.createNotificationChannel(channel);
    }

    private Notification buildRunningNotification() {
        return new NotificationCompat.Builder(this, "test_service_channel")
                .setContentTitle("自动化测试执行中")
                .setContentText("已完成 0/" + 4)
                .setSmallIcon(R.drawable.ic_launcher2)
                .setOngoing(true)
                .setProgress(2, 0, false)
                .build();
    }

    private void showFinishConfirmationDialog() {
        // 显示关闭对话框
        String title = getContext().getString(R.string.executor_finish_confirm_title);
        String message = getContext().getString(R.string.executor_finish_confirm_message);
        String positiveButtonText = getContext().getString(R.string.executor_finish_confirm_stop_button);
        String negativeButtonText = getContext().getString(R.string.executor_finish_confirm_continue_button);

        DialogHelper.getInstance().showDialog(getContext(), title, message,
                positiveButtonText, negativeButtonText, (xDialog, buttonId) -> {
                    xDialog.dismiss();
                    switch (buttonId) {
                        case XDialogInterface.BUTTON_POSITIVE:
                            if (testExecutor != null) {
                                testExecutor.stopExecution();
                            }
                            mWasManuallyStopped = true;
                            finishTest();
                            break;

                        case XDialogInterface.BUTTON_NEGATIVE:
                            // Continue the test
                            if (testExecutor != null) {
                                testExecutor.resumeExecution();
                            }
                            break;
                    }
                });
    }

    // 暴露LiveData供观察
//    public LiveData<TestResultEntity> getResultLiveData() {
//        return resultLiveData;
//    }

    private final IBinder binder = new LocalBinder();
    @Override
    public IBinder onBind(Intent intent) {
        FileLogger.i(TAG, "onBind()");
        return binder;
    }

    public class LocalBinder extends Binder {
        public TestExecutionService getService() {
            return TestExecutionService.this;
        }
    }



    /**
     * 将日志处理结果应用到TestFinishEntity
     * @param testFinishEntity 测试完成实体
     * @param logResult 日志处理结果
     */
    private void applyLogResultsToEntity(TestFinishEntity testFinishEntity, LogProcessResult logResult) {
        if (logResult == null) {
            FileLogger.w(TAG, "LogProcessResult is null");
            return;
        }

        try {
            if (logResult.getFileLogPath() != null) {
                testFinishEntity.setLogPath(logResult.getFileLogPath());
            }
            if (logResult.getCduLogPath() != null) {
                testFinishEntity.setCduLogPath(logResult.getCduLogPath());
            }
        } catch (Exception e) {
            // 应用结果异常不影响主流程
            FileLogger.e(TAG, "Error applying log results to entity", e);
        }
    }

    @Override
    public void onDestroy() {
        FileLogger.i(TAG, "onDestroy()");
        executor.shutdownNow();
        // Unregister EventBus
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        // 停止Can数据采集服务
        stopService(new Intent(this, CanDataCollectService.class));
        super.onDestroy();
    }
}
