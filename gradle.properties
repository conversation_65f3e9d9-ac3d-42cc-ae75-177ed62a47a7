# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx4g -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
systemProp.file.encoding=UTF-8

#debug
KEYSTORE_PASSWORD=zyxie1324
KEY_PASSWORD=zyxie1324
KEY_ALIAS=androiddebugkey

# 小鹏maven仓库地址
RELEASE_REPO_URL=http://maven.xiaopeng.local/repository/xp_android_release
SNAPSHOT_REPO_URL=http://maven.xiaopeng.local/repository/xp_android_snapshots
android.enableJetifier=true
android.useAndroidX=true

# build version
COMPILE_SDK_VERSION = 30
TARGET_SDK_VERSION = 30
MIN_SDK_VERSION = 28

# 公共组件库车型
public_libs_dependency_model=XOS5.0
# 公共组件库版本
public_libs_dependency_ver=5.6.0
# 是否忽略公共组件库：false-强制使用公共组件库版本，true-不使用公共组件库版本
ignore_pub_libs=true

# mock can报文解析
MOCK=false

# 开发环境：dev，测试环境：test，预发环境：uat，生产环境：prod
ENV_TYPE="prod"