[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_play_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\play_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_bg_topic_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\bg_topic_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-xxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_bg_topic_index.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\bg_topic_index.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-hdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_bg_recyclerview_scrollbar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\bg_recyclerview_scrollbar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_bg_test_result_table.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\bg_test_result_table.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\layout_ly_floating_window.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\layout\\ly_floating_window.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_ic_rerun_errors.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\ic_rerun_errors.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-mdpi_icon_xlogo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-mdpi\\icon_xlogo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-xhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_play.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\play.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_cell_border.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\cell_border.xml"}, {"merged": "com.xiaopeng.xpautotest.app-merged_res-47:/layout_at_fragment_tab.xml.flat", "source": "com.xiaopeng.xpautotest.app-main-49:/layout/at_fragment_tab.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap_img_topic_item_cover_default.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap\\img_topic_item_cover_default.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\layout_at_layout_dialog_loading.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\layout\\at_layout_dialog_loading.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-mdpi_icon_status_pending.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-mdpi\\icon_status_pending.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\layout_at_activity_factory.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\layout\\at_activity_factory.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\layout_at_layout_factory_tab_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\layout\\at_layout_factory_tab_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_run_errors.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\run_errors.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-xxxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_ic_rerun.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\ic_rerun.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_ic_screen_record.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\ic_screen_record.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-mdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_refresh.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\refresh.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-mdpi_icon_maintain.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-mdpi\\icon_maintain.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-mdpi_icon_status_failure.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-mdpi\\icon_status_failure.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable-v24_bwater_top_input.9.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable-v24\\bwater_top_input.9.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\layout_at_fragment_loading_dialog.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\layout\\at_fragment_loading_dialog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_ic_launcher2.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\ic_launcher2.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\layout_at_floating_debug_mode.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\layout\\at_floating_debug_mode.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_ic_record_red.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\ic_record_red.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-night-mdpi_icon_xlogo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-night-mdpi\\icon_xlogo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\layout_at_fragment_factory_sub_directory.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\layout\\at_fragment_factory_sub_directory.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-mdpi_icon_status_skipped.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-mdpi\\icon_status_skipped.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\layout_activity_screen_record_test.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\layout\\activity_screen_record_test.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_border.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\border.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable-v24_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable-v24\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_ic_stop_record.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\ic_stop_record.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_ic_debug.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\ic_debug.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_floating_window_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\floating_window_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\xml_service_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\xml\\service_config.xml"}, {"merged": "com.xiaopeng.xpautotest.app-merged_res-47:/layout_at_fragment_factory_sub_directory.xml.flat", "source": "com.xiaopeng.xpautotest.app-main-49:/layout/at_fragment_factory_sub_directory.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_ic_rerun.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\ic_rerun.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_debug_rerun.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\debug_rerun.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-mdpi_icon_status_success.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-mdpi\\icon_status_success.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-xxhdpi_bwater_top_input.9.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-xxhdpi\\bwater_top_input.9.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\layout_at_fragment_tab.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\layout\\at_fragment_tab.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\drawable_run_all.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\drawable\\run_all.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\layout_floating_recording_window.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\layout\\floating_recording_window.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\layout_at_layout_factory_title.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\layout\\at_layout_factory_title.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\layout_at_layout_factory_component_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\layout\\at_layout_factory_component_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-mdpi_icon_status_execute.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-mdpi\\icon_status_execute.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-merged_res-47:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.xiaopeng.xpautotest.app-main-49:\\mipmap-hdpi\\ic_launcher.webp"}]