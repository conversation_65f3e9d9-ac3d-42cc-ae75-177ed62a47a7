<?xml version="1.0" encoding="utf-8"?>
<com.xiaopeng.xui.widget.XConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.xiaopeng.xui.widget.XLinearLayout
        android:id="@+id/ll_execute"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.xiaopeng.xui.widget.XImageView
            android:id="@+id/btn_debug"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginTop="8dp"
            android:layout_marginLeft="10dp"
            android:gravity="center"
            android:src="@drawable/ic_debug" />

        <com.xiaopeng.xui.widget.XTextView
            android:id="@+id/tv_tab_title"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="match_parent"
            android:layout_marginLeft="10dp"
            android:textColor="@color/x_theme_text_01"
            android:textSize="@dimen/x_font_title_03_size"
            android:textAlignment="center"
            android:gravity="center"
            android:singleLine="true" />

        <com.xiaopeng.xui.widget.XImageView
            android:id="@+id/btn_execute"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginTop="8dp"
            android:layout_marginLeft="10dp"
            android:gravity="center"
            android:src="@drawable/x_mix_ic_small_video"/>

        <com.xiaopeng.xui.widget.XImageView
            android:id="@+id/btn_retry_all_failed"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginTop="8dp"
            android:layout_marginLeft="10dp"
            android:gravity="center"
            android:src="@drawable/ic_common_refresh_o"
            android:visibility="gone" />

    </com.xiaopeng.xui.widget.XLinearLayout>

    <com.xiaopeng.xui.widget.XRecyclerView
        android:id="@+id/rv_test_suite_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="80dp"
        android:scrollbarSize="8dp"
        android:scrollbarThumbVertical="@drawable/x_thumb_scrollbar_vertical"
        android:scrollbars="none"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_execute" />

</com.xiaopeng.xui.widget.XConstraintLayout>