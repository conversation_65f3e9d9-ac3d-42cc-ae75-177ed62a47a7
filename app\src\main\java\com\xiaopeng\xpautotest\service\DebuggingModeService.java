package com.xiaopeng.xpautotest.service;

import android.app.Service;
import android.content.Intent;
import android.os.Binder;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.widget.Toast;

import com.xiaopeng.executor.TestExecutor;
import com.xiaopeng.executor.BaseTestExecutor;
import com.xiaopeng.lib.utils.ThreadUtils;
import com.xiaopeng.xpautotest.R;
import com.xiaopeng.xpautotest.bean.ReportableEntity;
import com.xiaopeng.xpautotest.bean.StepResultEntity;
import com.xiaopeng.xpautotest.bean.TestResultEntity;
import com.xiaopeng.xpautotest.bean.TestScriptEntity;
import com.xiaopeng.xpautotest.bean.TestStartedEntity;
import com.xiaopeng.xpautotest.bean.TestStopEntity;
import com.xiaopeng.xpautotest.bean.TestTaskBean;
import com.xiaopeng.xpautotest.bean.TraceResultEntity;
import com.xiaopeng.xpautotest.bean.UploadFileTask;
import com.xiaopeng.xpautotest.client.FileDownloader;
import com.xiaopeng.xpautotest.client.TestReporterSDK;
import com.xiaopeng.xpautotest.client.websocket.CustomWebSocketListener;
import com.xiaopeng.xpautotest.client.websocket.WebSocketManager;
import com.xiaopeng.xpautotest.community.bean.CaseExecuteState;
import com.xiaopeng.xpautotest.community.event.TraceEvent;
import com.xiaopeng.xpautotest.community.event.TraceResultState;
import com.xiaopeng.xpautotest.community.test.FailureReasonDetail;
import com.xiaopeng.xpautotest.community.test.TestResult;
import com.xiaopeng.xpautotest.community.test.TestScript;
import com.xiaopeng.xpautotest.community.test.TestStep;
import com.xiaopeng.xpautotest.community.utils.Constant;
import com.xiaopeng.xpautotest.community.utils.FileLogger;
import com.xiaopeng.xpautotest.community.utils.FileUtils;
import com.xiaopeng.xpautotest.community.utils.Log;
import com.xiaopeng.xpautotest.constant.EnvironmentConfig;
import com.xiaopeng.xpautotest.helper.DialogHelper;
import com.xiaopeng.xpautotest.helper.TestDataStorageHelper;
import com.xiaopeng.xpautotest.trace.service.CanDataCollectService;
import com.xiaopeng.xpautotest.ui.DebugModeFloatingView;
import com.xiaopeng.xpautotest.ui.MainActivity;
import com.xiaopeng.xpautotest.utils.CarUtils;
import com.xiaopeng.xpautotest.utils.GsonUtils;
import com.xiaopeng.xpautotest.utils.OSSPathUtils;
import com.xiaopeng.xpautotest.utils.SystemPropertiesUtils;
import com.xiaopeng.xui.app.XDialogInterface;
import android.car.XpCarFeatures;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Response;
import okhttp3.WebSocket;

import static com.xiaopeng.xui.Xui.getContext;


public class DebuggingModeService extends Service {
    private static final String TAG = "DebuggingModeService";
    private TestExecutor testExecutor;

    private ExecutorService executor;
    private TestTaskBean mTaskInfo;
    private DebugModeFloatingView mDebugModeView;
    WebSocketManager webSocketManager;
    private volatile boolean mIsCanParseSuccess; // Can信号解析结果

    // 批量上报优化：当前脚本的步骤和CAN数据收集
    private List<StepResultEntity> mCurrentScriptStepResults = new ArrayList<>();
    private List<TraceResultEntity> mCurrentScriptTraceResults = new ArrayList<>();

    @Override
    public void onCreate() {
        super.onCreate();
        FileLogger.i(TAG, "onCreate()");
        executor = Executors.newSingleThreadExecutor();
        // 绑定事件监听
        EventBus.getDefault().register(this);
        // 启动Can数据采集服务
        Intent intent = new Intent(this, CanDataCollectService.class);
        startService(intent);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        FileLogger.i(TAG, "onStartCommand()");
        if (intent == null) {
            FileLogger.e(TAG, "intent is null");
            return START_NOT_STICKY;
        }

        if (mTaskInfo == null) {
            mTaskInfo = TestDataStorageHelper.getInstance().loadTaskSync();
        }

        initWebSocket();

        return super.onStartCommand(intent, flags, startId);
    }

    private void initWebSocket() {
        String wsUrl = EnvironmentConfig.getInstance().getWSUrl(SystemPropertiesUtils.getVIN(), CarUtils.getCarType(), XpCarFeatures.getSoftwareVersionCode());
        String token = EnvironmentConfig.getInstance().getApiKey();
        Log.i(TAG, "WebSocket URL: " + wsUrl);
        
        webSocketManager = new WebSocketManager(
                wsUrl,
                token, // Replace with your actual token
                new CustomWebSocketListener() {
                    @Override
                    public void onOpen(WebSocket webSocket, Response response) {
                        FileLogger.i(TAG, "WebSocket opened: " + response.message());
                    }

                    @Override
                    public void onMessage(WebSocket webSocket, JSONObject json) {
                        FileLogger.i(TAG, "Received message: " + json.toString());
                        // Example of sending a message back to the server
                        String type = json.optString("type");
                        if ("control".equals(type)) {
                            JSONObject jsonContent = json.optJSONObject("content");
                            if (jsonContent == null) {
                                FileLogger.e(TAG, "Received control message without content");
                                return;
                            }
                            JSONArray fileList = jsonContent.optJSONArray("file_urls");
                            if (fileList != null && fileList.length() > 0) {
                                FileLogger.i(TAG, "Received control message with fileList: " + fileList.toString());
                                downloadFiles(fileList);
                            }
                            TestScriptEntity testScript = new TestScriptEntity(1, jsonContent.optString("steps"));
                            startExecutor(testScript);
                        } else if ("connection".equals(type)) {
                            Log.i(TAG, "connected to server successfully");
                            ThreadUtils.runOnMainThread(() -> {
                                openDebugModeView();
                            });
                        } else {
                            // Handle other message types if needed
                            FileLogger.i(TAG, "Received unknown message type: " + type);
                        }
//                        JSONObject message = new JSONObject();
//                        try {
//                            message.put("type", "report_message");
//                            message.put("message", "vehicle report message.");
//                            webSocket.send(message.toString());
//                        } catch (Exception e) {
//                            FileLogger.e(TAG, "Error sending message: " + e.getMessage());
//                        }
                    }

                    @Override
                    public void onClosed(WebSocket webSocket, int code, String reason) {
                        Log.i(TAG, "WebSocket closed: " + reason);
                        finishDebug();
                    }

                    @Override
                    public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                        Log.e(TAG, "WebSocket failure: " + t.getMessage());
                        if (response != null) {
                            Log.e(TAG, "Response: " + response.message());
                        }

                        // Show a toast message to notify the user
                        ThreadUtils.runOnMainThread(() -> {
////                            new Handler(Looper.getMainLooper()).post(() ->
                                    Toast.makeText(getApplicationContext(), "连接服务器失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
////                            );
                        });

                        finishDebug();

//                        if (t instanceof java.net.SocketException || t.getMessage().contains("Connection reset")){
//
//                        }
                    }
                }
        );

        // 配置日志回调，发送给WebSocket服务器
        FileLogger.setLogCallback((level, message) -> {
            if (webSocketManager != null) {
                try {
                    JSONObject json = new JSONObject();
                    json.put("level", level);
                    json.put("type", "log_message");
                    json.put("content", message);
                    webSocketManager.sendMessage(json.toString());
                } catch (Exception e) {
                    FileLogger.e(TAG, "Failed to send log via WebSocket: " + e.getMessage());
                }
            }
        });
    }

    /**
     * 启动执行服务
     * */
    private void startExecutor(TestScriptEntity testScript) {
        FileLogger.i(TAG, "startExecutor: script=" + testScript.getScriptId());

        executor.execute(() -> {
            testExecutor = new TestExecutor(getApplicationContext(), getPackageName());
            testExecutor.addExecuteHandler(mExecuteHandler);

            mIsCanParseSuccess = true; // 清空上次执行的Can结果

            // 清空当前脚本的收集列表
            mCurrentScriptStepResults.clear();
            mCurrentScriptTraceResults.clear();

            TestResultEntity result = executeSingleTestScript(testScript);

            // 直接设置收集的步骤和CAN数据列表
            result.setStepResults(new ArrayList<>(mCurrentScriptStepResults));
            result.setTraceResults(new ArrayList<>(mCurrentScriptTraceResults));

            reportMessage(result);
            if (testExecutor != null) {
                testExecutor.removeExecuteHandler();
            }
        });
    }

    /**
     * 执行单个测试脚本
     * */
    private TestResultEntity executeSingleTestScript(TestScriptEntity testScript) {
        boolean isNeedTrace = testScript.needTrace();
        if (isNeedTrace) {
            EventBus.getDefault().post(new TraceEvent.StartEvent());
        }
        long startTime = System.currentTimeMillis();
        int result = testExecutor.runTestScript(testScript.getScriptId(), testScript.getSteps());
        long endTime = System.currentTimeMillis();
        if (isNeedTrace) {
            // 发送测试结束事件，同步执行
            FileLogger.i(TAG, "Send EndEvent: " + testScript.getScriptId());
            EventBus.getDefault().post(new TraceEvent.EndEvent()); // 发送测试结束事件
        }
        if (!mIsCanParseSuccess && result != CaseExecuteState.SKIPPED) {
            result = CaseExecuteState.FAILURE; // 如果Can数据解析失败，将结果设置为失败
        }
        // int status = ((result == CaseExecuteState.SUCCESS) && mIsCanParseSuccess) ? CaseExecuteState.SUCCESS : CaseExecuteState.FAILURE;
        FileLogger.i(TAG, "finish testcase: " + testScript.getScriptId() + ", result: " + result + ", canParseResult: " + mIsCanParseSuccess);
        return new TestResultEntity(mTaskInfo.getExecutionId(), testScript.getScriptId(), result, startTime, endTime, "");
    }

    private final BaseTestExecutor.ExecutionHandler mExecuteHandler = new BaseTestExecutor.ExecutionHandler() {
        @Override
        public void onStepSuccess(TestStep testStep, TestResult testResult) {
            StepResultEntity stepResult = new StepResultEntity(mTaskInfo.getExecutionId(), testStep.getScriptId(), testStep.getStepId(), testStep.getStep(),testStep.getComments(),testStep.getKeywords(),testStep.getStepType(),"PASS", testResult.getStartTime(), testResult.getEndTime());
            if (testResult.getData() instanceof TestResult.ActionArtifacts) {
                setStepArtifacts(stepResult, testResult);
            }

            // 批量上报优化：收集到列表中，不立即上报
            mCurrentScriptStepResults.add(stepResult);
            FileLogger.i(TAG, "Collected step result for batch reporting: scriptId=" + testStep.getScriptId() + ", stepId=" + testStep.getStepId());
        }



        @Override
        public void onStepFailure(TestStep testStep, TestResult testResult) {
            StepResultEntity stepResult = new StepResultEntity(mTaskInfo.getExecutionId(), testStep.getScriptId(), testStep.getStepId(),testStep.getStep(),testStep.getComments(),testStep.getKeywords(),testStep.getStepType(), "FAIL", testResult.getStartTime(), testResult.getEndTime());
            FailureReasonDetail failureReason = testResult.getFailureReason();
            if (failureReason != null) {
                stepResult.setFailureReason(failureReason);
            }
            setStepArtifacts(stepResult, testResult);

            // 批量上报优化：收集到列表中，不立即上报
            mCurrentScriptStepResults.add(stepResult);
            FileLogger.i(TAG, "Collected step failure result for batch reporting: scriptId=" + testStep.getScriptId() + ", stepId=" + testStep.getStepId());
        }

        private void setStepArtifacts(StepResultEntity stepResult, TestResult testResult) {
            if (testResult.getData()!=null && testResult.getData() instanceof TestResult.ActionArtifacts) {
                TestResult.ActionArtifacts artifactsData = (TestResult.ActionArtifacts) testResult.getData();
                String originalScreenshotFileName = artifactsData.imgFileName;
                String originalUiDumpFileName = artifactsData.uiDumpFileName;
                if (originalScreenshotFileName != null && !originalScreenshotFileName.isEmpty()) {
                    String imgPath = OSSPathUtils.buildObjectKey(UploadFileTask.FileType.FILE_IMAGE, originalScreenshotFileName, null);
                    stepResult.setImgPath(imgPath);
                }
                if (originalUiDumpFileName != null && !originalUiDumpFileName.isEmpty()) {
                    String uiDumpPath = OSSPathUtils.buildObjectKey(UploadFileTask.FileType.FILE_XML, originalUiDumpFileName, null);
                    stepResult.setDumpPath(uiDumpPath);
                }
            }
        }

        @Override
        public void onScriptSuccess(TestScript testScript) {

        }
    };

    /**
     * 接收trace解析结果
     * */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onTraceResultEvent(TraceEvent.ResultEvent event) {
        FileLogger.i(TAG, "onTraceResultEvent: " + event.toString());
        TraceResultEntity traceResult = new TraceResultEntity(mTaskInfo.getExecutionId(), event.getScriptId(), event.getStepId(), event.getTraceId(), event.getType(), event.getSrc(), event.getValue(), event.getResult(), event.getMessage());
        if (!event.getResult().equals(TraceResultState.OK)) {
            // 如果结果不是OK，记录Can数据采集结果
            mIsCanParseSuccess = false;
        }

        // 批量上报优化：收集到列表中，不立即上报
        mCurrentScriptTraceResults.add(traceResult);
        FileLogger.i(TAG, "Collected trace result for batch reporting: scriptId=" + event.getScriptId() + ", stepId=" + event.getStepId() + ", traceId=" + event.getTraceId());
    }

    private void reportMessage(ReportableEntity result) {
        if (webSocketManager != null) {
            String resultJson = GsonUtils.toJson(result, TAG);
            JSONObject json = new JSONObject();
            try {
                json.put("type", "report_message");
                json.put("content", resultJson);
                webSocketManager.sendMessage(json.toString());
            } catch (Exception e) {
                FileLogger.e(TAG, "Error sending message: " + e.getMessage());
            }
        } else {
            FileLogger.e(TAG, "WebSocketManager is not initialized.");
        }
    }

    private void downloadFiles(JSONArray fileList) {
        String fileParentPath = Constant.AUTOTEST_TEMPLATE_ICON_PATH;
        if (!FileUtils.isFolderExists(fileParentPath)) {
            return;
        }
        for (int i = 0; i < fileList.length(); i++) {
            JSONObject fileJson  = fileList.optJSONObject(i);
            if (fileJson != null) {
                String url = fileJson.optString("url");
                String filePath = fileParentPath + fileJson.optString("name");
                FileLogger.i(TAG, "Downloading file from URL: " + url);
                // 异步下载文件
                FileDownloader.downloadFileAsync(url, filePath, new FileDownloader.DownloadCallback() {
                    @Override
                    public void onSuccess(String localFilePath) {
                        FileLogger.i(TAG, "File downloaded successfully: " + localFilePath);
                    }

                    @Override
                    public void onFailure(Exception e) {
                        FileLogger.e(TAG, "File download failed: " + e.getMessage());
                    }
                });
            }
        }
    }

    private void openDebugModeView() {
        if (mDebugModeView == null) {
            mDebugModeView = new DebugModeFloatingView(this, () -> {
                FileLogger.w(TAG, "onClicked testing floating view");
                if (testExecutor != null) {
                    this.testExecutor.pauseExecution();
                }
                showFinishConfirmationDialog();
            });
        }
    }

    private void finishDebug() {
        Log.i(TAG, "finishDebug()");
        // 1. 关闭websocket连接
        if (webSocketManager != null) {
            webSocketManager.close();
            webSocketManager = null;
        }
        // 2. 先关闭悬浮窗，清理当前UI
        if (mDebugModeView != null) {
            mDebugModeView.close();
            mDebugModeView = null;
        }
        // 3. 启动自动化Activity
        Intent intent = new Intent(DebuggingModeService.this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        intent.putExtra(Constant.EXTRA_TEST_DATA_LOAD_TAG, "local");
        startActivity(intent);

        // 4. 最后清理Service自身
        if (executor != null) {
            executor.shutdownNow();
        }
        stopSelf();
    }

    private void showFinishConfirmationDialog() {
        String title = getContext().getString(R.string.executor_debug_confirm_title);
        String message = getContext().getString(R.string.executor_debug_confirm_message);
        String positiveButtonText = getContext().getString(R.string.executor_finish_confirm_stop_button);
        String negativeButtonText = getContext().getString(R.string.executor_finish_confirm_continue_button);

        DialogHelper.getInstance().showDialog(getContext(), title, message,
                positiveButtonText, negativeButtonText, (xDialog, buttonId) -> {
                    xDialog.dismiss();
                    switch (buttonId) {
                        case XDialogInterface.BUTTON_POSITIVE:
                            if (testExecutor != null) {
                                testExecutor.stopExecution();
                            }
                            finishDebug();
                            break;

                        case XDialogInterface.BUTTON_NEGATIVE:
                            // Continue the test
                            if (testExecutor != null) {
                                testExecutor.resumeExecution();
                            }
                            break;
                    }
                });
    }

    private final IBinder binder = new DebuggingModeService.LocalBinder();
    @Override
    public IBinder onBind(Intent intent) {
        FileLogger.i(TAG, "onBind()");
        return binder;
    }

    public class LocalBinder extends Binder {
        public DebuggingModeService getService() {
            return DebuggingModeService.this;
        }
    }

    @Override
    public void onDestroy() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdownNow();
        }
        // 解绑事件监听
        EventBus.getDefault().unregister(this);
        // 停止Can数据采集服务
        stopService(new Intent(this, CanDataCollectService.class));
        super.onDestroy();
    }
}
