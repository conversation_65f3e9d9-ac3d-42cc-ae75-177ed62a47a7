# TestReporterSDK 数据上报分析

## 概述

TestReporterSDK 是测试框架中负责数据上报的核心组件，通过 `reportStep()` 方法将测试执行过程中的各种数据实体上报到测试平台。本文档详细分析了 TestReporterSDK 在各个测试环节上报的数据类型、时机和内容。

## 上报数据类型概览

TestReporterSDK 支持上报以下 6 种数据实体类型：

1. **TestStartedEntity** - 测试开始实体
2. **TestResultEntity** - 测试脚本执行结果实体
3. **StepResultEntity** - 测试步骤执行结果实体
4. **TraceResultEntity** - CAN数据追踪结果实体
5. **TestFinishEntity** - 测试完成实体
6. **TestStopEntity** - 测试停止实体

## 各环节数据上报详情

### 1. 测试开始阶段 - TestStartedEntity

#### 上报时机
- 测试任务启动时，在 `TestExecutionService.onStartCommand()` 中上报

#### 上报位置
```java
// TestExecutionService.java
mTaskExecutionInfo = new TestStartedEntity(mTaskInfo.getExecutionId(), taskType, mTestStartTimestamp, mTaskInfo.getSuiteId(), pendingScriptIdList);
TestReporterSDK.reportStep(mTaskExecutionInfo);
```

#### 上报数据内容
| 字段 | 类型 | 说明 |
|------|------|------|
| taskExecutionId | long | 任务执行ID |
| taskType | int | 任务类型（1=套件/全部，2=单个脚本） |
| triggerTimestamp | long | 触发时间戳 |
| suiteId | long | 测试套件ID |
| pendingScriptIdList | ArrayList<Long> | 待执行脚本ID列表 |
| testCount | int | 测试脚本总数 |
| okCount | int | 成功计数（初始为0） |
| ngCount | int | 失败计数（初始为0） |
| naCount | int | 跳过计数（初始为0） |

### 2. 脚本执行阶段 - TestResultEntity

#### 上报时机
1. **脚本开始执行时**：状态为 `EXECUTING`
2. **脚本执行完成时**：状态为 `SUCCESS/FAILURE/SKIPPED`

#### 上报位置
```java
// 脚本开始执行
TestReporterSDK.reportStep(new TestResultEntity(mTaskInfo.getExecutionId(), testScript.getScriptId(), CaseExecuteState.EXECUTING));

// 脚本执行完成
private void reportTestResult(TestResultEntity result) {
    TestReporterSDK.reportStep(result);
}
```

#### 上报数据内容
| 字段 | 类型 | 说明 |
|------|------|------|
| taskExecutionId | long | 任务执行ID |
| id | long | 脚本ID |
| status | int | 执行状态（EXECUTING/SUCCESS/FAILURE/SKIPPED） |
| startTime | long | 开始时间 |
| endTime | long | 结束时间 |
| duration | long | 执行时长 |
| triggerTimestamp | long | 触发时间戳 |
| suiteId | long | 测试套件ID |
| description | String | 描述信息 |
| **stepResults** | **List<StepResultEntity>** | **批量上报优化：步骤执行结果列表** |
| **traceResults** | **List<TraceResultEntity>** | **批量上报优化：CAN数据追踪结果列表** |

### 3. 步骤执行阶段 - StepResultEntity

#### 上报时机 (批量上报优化后)
1. **步骤执行成功时**：通过 `onStepSuccess()` 回调收集到列表，不立即上报
2. **步骤执行失败时**：通过 `onStepFailure()` 回调收集到列表，不立即上报
3. **脚本执行完成时**：随 `TestResultEntity` 一起批量上报

#### 上报位置 (批量上报优化后)
```java
// 步骤成功 - 收集到列表，不立即上报
@Override
public void onStepSuccess(TestStep testStep, TestResult testResult) {
    StepResultEntity stepResult = new StepResultEntity(...);
    if (testResult.getData() instanceof TestResult.ActionArtifacts) {
        setStepArtifacts(stepResult, testResult);
    }
    // 批量上报优化：收集到列表中，不立即上报
    mCurrentScriptStepResults.add(stepResult);
}

// 步骤失败 - 收集到列表，不立即上报
@Override
public void onStepFailure(TestStep testStep, TestResult testResult) {
    StepResultEntity stepResult = new StepResultEntity(...);
    FailureReasonDetail failureReason = testResult.getFailureReason();
    if (failureReason != null) {
        stepResult.setFailureReason(failureReason);
    }
    setStepArtifacts(stepResult, testResult);
    // 批量上报优化：收集到列表中，不立即上报
    mCurrentScriptStepResults.add(stepResult);
}

// 脚本完成时批量上报
TestResultEntity result = executeSingleTestScript(testScript);
result.setStepResults(new ArrayList<>(mCurrentScriptStepResults));
TestReporterSDK.reportStep(result); // 包含所有步骤结果的批量上报
```

#### 上报数据内容
| 字段 | 类型 | 说明 |
|------|------|------|
| taskExecutionId | long | 任务执行ID |
| scriptId | long | 脚本ID |
| stepId | int | 步骤ID |
| step | String | 步骤内容 |
| comment | String | 步骤注释 |
| keywords | String | 步骤关键字 |
| stepType | String | 步骤类型（Precondition/Procedure/PostCondition） |
| stepResult | String | 步骤结果（PASS/FAIL） |
| startTime | long | 开始时间 |
| endTime | long | 结束时间 |
| imgPath | String | 截图OSS路径 |
| dumpPath | String | UI转储OSS路径 |
| canPath | String | CAN数据OSS路径 |
| failureReason | FailureReasonDetail | 失败原因详情（仅失败时） |

### 4. CAN数据采集阶段 - TraceResultEntity

#### 上报时机 (批量上报优化后)
- 接收到CAN数据解析结果时，通过 `onTraceResultEvent()` EventBus事件收集到列表，不立即上报
- 脚本执行完成时，随 `TestResultEntity` 一起批量上报

#### 上报位置 (批量上报优化后)
```java
@Subscribe(threadMode = ThreadMode.BACKGROUND)
public void onTraceResultEvent(TraceEvent.ResultEvent event) {
    TraceResultEntity traceResult = new TraceResultEntity(...);
    // 批量上报优化：收集到列表中，不立即上报
    mCurrentScriptTraceResults.add(traceResult);
}

// 脚本完成时批量上报
TestResultEntity result = executeSingleTestScript(testScript);
result.setTraceResults(new ArrayList<>(mCurrentScriptTraceResults));
TestReporterSDK.reportStep(result); // 包含所有CAN数据的批量上报
```

#### 上报数据内容
| 字段 | 类型 | 说明 |
|------|------|------|
| taskExecutionId | long | 任务执行ID |
| scriptId | long | 脚本ID |
| stepId | int | 步骤ID |
| traceId | String | 追踪ID |
| type | String | 数据类型 |
| src | String | 数据源 |
| value | String | 数据值 |
| result | String | 解析结果状态 |
| message | String | 消息内容 |

### 5. 测试完成阶段 - TestFinishEntity

#### 上报时机
- 所有测试脚本执行完成后，在 `finishTest()` 方法中上报

#### 上报位置
```java
public void finishTest() {
    TestFinishEntity testFinishEntity = new TestFinishEntity(...);
    
    // 日志处理
    LogProcessResult logResult = mLogProcessHelper.processAllLogs(...);
    applyLogResultsToEntity(testFinishEntity, logResult);
    
    // 上报测试完成实体（包含CDU日志路径）
    TestReporterSDK.reportStep(testFinishEntity);
}
```

#### 上报数据内容
| 字段 | 类型 | 说明 |
|------|------|------|
| taskExecutionId | long | 任务执行ID |
| triggerTimestamp | long | 触发时间戳 |
| suiteId | long | 测试套件ID |
| testCount | int | 测试总数 |
| okCount | int | 成功计数 |
| ngCount | int | 失败计数 |
| naCount | int | 跳过计数 |
| logPath | String | FileLogger日志OSS路径 |
| cduLogPath | String | CDU日志OSS路径 |
| scriptIdList | ArrayList<Long> | 执行的脚本ID列表 |

### 6. 测试停止阶段 - TestStopEntity

#### 上报时机
- 手动停止测试时，在 `finishTest()` 方法中条件性上报

#### 上报位置
```java
public void finishTest() {
    if (mWasManuallyStopped) {
        TestReporterSDK.reportStep(new TestStopEntity(mTaskInfo.getExecutionId()));
    }
}
```

#### 上报数据内容
| 字段 | 类型 | 说明 |
|------|------|------|
| taskExecutionId | long | 任务执行ID |

## 数据上报流程图

### 优化后的批量上报流程

```
测试启动 → TestStartedEntity
    ↓
脚本开始 → TestResultEntity (EXECUTING)
    ↓
步骤执行 → 收集到 stepResults 列表 (不立即上报)
CAN数据  → 收集到 traceResults 列表 (不立即上报)
    ↓
脚本完成 → TestResultEntity (包含 stepResults + traceResults) → 一次性上报
    ↓
测试完成 → TestFinishEntity
    ↓
手动停止 → TestStopEntity (可选)
```

### 优化前的实时上报流程 (已废弃)

```
测试启动 → TestStartedEntity
    ↓
脚本开始 → TestResultEntity (EXECUTING)
    ↓
步骤执行 → StepResultEntity (PASS/FAIL) - 立即上报
CAN数据  → TraceResultEntity - 立即上报
    ↓
脚本完成 → TestResultEntity (SUCCESS/FAILURE/SKIPPED)
    ↓
测试完成 → TestFinishEntity
    ↓
手动停止 → TestStopEntity (可选)
```

## 核心特点

### 1. 批量上报优化 (新增)
- 步骤和CAN数据收集到列表中，脚本完成时批量上报
- 减少网络请求频次，提升测试执行性能
- 保持数据完整性和API兼容性

### 2. 脚本级实时上报
- 脚本状态变化时实时更新（开始执行、执行完成）
- 测试任务级别的实时监控

### 3. 完整状态跟踪
- 从测试开始到结束的完整状态链路
- 支持测试进度实时监控

### 4. 数据丰富性
- 包含执行时间、失败原因、文件路径等详细信息
- 支持截图、UI转储、CAN数据等多媒体文件关联

### 5. 容错机制
- 网络不可用时本地缓存数据
- 网络恢复后批量上报
- 支持重试机制

### 6. 分类处理
- 不同类型数据调用不同的API接口
- 统一的队列管理和异步处理

## API接口映射

| 数据实体 | API接口 |
|----------|---------|
| TestStartedEntity | reportTestStartedSync() |
| TestResultEntity | reportTestResultSync() |
| StepResultEntity | reportStepResultSync() |
| TraceResultEntity | reportTraceResultSync() |
| TestFinishEntity | reportTestFinishSync() |
| TestStopEntity | reportTestStopSync() |

## 总结

TestReporterSDK 的数据上报机制设计完善，通过批量上报优化后能够：

1. **全面覆盖**：涵盖测试执行的所有关键环节
2. **数据完整**：提供详细的执行信息和相关文件
3. **性能优化**：批量上报减少网络请求频次，提升执行效率
4. **实时监控**：支持脚本级和任务级的实时状态跟踪
5. **可靠性高**：具备完善的容错和重试机制
6. **扩展性好**：支持多种数据类型和文件格式
7. **向后兼容**：API接口保持不变，平滑升级

这为测试平台提供了完整的数据支撑，在确保测试分析和问题定位有效性的同时，显著提升了系统性能。
